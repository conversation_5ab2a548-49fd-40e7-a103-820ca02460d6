import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata, generateJsonLd } from '@/lib/seo';
import { 
  Zap, 
  Shield, 
  BarChart3, 
  Code, 
  Globe, 
  Users,
  Brain,
  Rocket,
  Lock
} from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Características para PyMES de E-commerce - Rayuela',
  description: 'Funcionalidades diseñadas para PyMES de E-commerce y Contenido Digital: recomendaciones IA, integración Mercado Pago, explicabilidad y más.',
  path: '/features',
  keywords: ['PyMES e-commerce', 'recomendaciones IA', 'Mercado Pago', 'explicabilidad ML', 'API e-commerce', 'personalización LATAM'],
});

const features = [
  {
    icon: Zap,
    title: "Aumenta tus Ventas Inmediatamente",
    description: "Recomendaciones personalizadas que impulsan la conversión y el valor promedio del carrito en tu e-commerce."
  },
  {
    icon: Brain,
    title: "IA de Nivel Enterprise para PyMES",
    description: "Algoritmos híbridos y Learning-to-Rank que antes solo tenían las grandes empresas, ahora accesibles para tu negocio."
  },
  {
    icon: Code,
    title: "Integración en Minutos, no Meses",
    description: "API-first diseñada por desarrolladores para desarrolladores. SDKs listos para Python, JavaScript y PHP."
  },
  {
    icon: Globe,
    title: "Optimizado para LATAM",
    description: "Integración nativa con Mercado Pago y precios adaptados al mercado local. Soporte en español."
  },
  {
    icon: Shield,
    title: "Explicaciones Claras de cada Recomendación",
    description: "Entiende por qué se recomienda cada producto. Sin 'cajas negras' - toma decisiones informadas sobre tu estrategia."
  },
  {
    icon: BarChart3,
    title: "Métricas que Importan para tu Negocio",
    description: "Dashboards claros con métricas de conversión, engagement y ROI. Ve el impacto real en tus ventas."
  },
  {
    icon: Users,
    title: "Crece sin Preocupaciones Técnicas",
    description: "Arquitectura multi-tenant que escala automáticamente desde 100 hasta millones de usuarios."
  },
  {
    icon: Rocket,
    title: "Resultados desde el Primer Día",
    description: "Implementación completa en horas. Empieza a ver mejoras en conversión desde las primeras recomendaciones."
  },
  {
    icon: Lock,
    title: "Tus Datos, tu Control",
    description: "Cumplimiento GDPR, privacidad por diseño y control total sobre tus datos. Sin vendor lock-in."
  }
];

export default function FeaturesPage() {
  const softwareSchema = generateJsonLd('SoftwareApplication', {
    name: 'Rayuela - Personalización E-commerce IA',
    description: 'Sistema de recomendaciones inteligentes para PyMES de E-commerce y Contenido Digital',
    featureList: features.map(f => f.title),
  });

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(softwareSchema),
        }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 py-16">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Funcionalidades Diseñadas para PyMES de E-commerce
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">
              Todas las herramientas que necesitas para competir con los grandes players del e-commerce, sin la complejidad ni los costos prohibitivos
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card key={index} className="h-full">
                  <CardHeader>
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
                      <Icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* CTA Section */}
          <div className="text-center bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              ¿Listo para Transformar tu E-commerce?
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
              Únete a las PyMES que ya están aumentando sus ventas con personalización inteligente
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button asChild size="lg">
                <Link href="/register">Regístrate Gratis y Prueba Rayuela</Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/contact-sales">Solicita Demo Personalizada</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
